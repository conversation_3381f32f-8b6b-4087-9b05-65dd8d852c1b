const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function generateAndroidIcons() {
  const iconPath = './src/assets/images/favicon.svg';

  if (!fs.existsSync(iconPath)) {
    console.error('Icon file not found:', iconPath);
    return;
  }

  const sizes = {
    'mipmap-mdpi': 48,
    'mipmap-hdpi': 72,
    'mipmap-xhdpi': 96,
    'mipmap-xxhdpi': 144,
    'mipmap-xxxhdpi': 192,
  };

  try {
    for (const [folder, size] of Object.entries(sizes)) {
      const outputDir = `./android/app/src/main/res/${folder}`;
      const outputPath = path.join(outputDir, 'ic_launcher.png');

      // Ensure directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      await sharp(iconPath).resize(size, size).png().toFile(outputPath);

      console.log(`✅ Generated ${folder}/ic_launcher.png (${size}x${size})`);
    }

    console.log('🎉 All Android icons generated successfully!');
  } catch (error) {
    console.error('❌ Error generating icons:', error);
  }
}

generateAndroidIcons();
