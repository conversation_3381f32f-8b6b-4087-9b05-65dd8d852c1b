import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay 
} from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onFinish: () => void;
}

export default function SplashScreen({ onFinish }: SplashScreenProps) {
  const logoOpacity = useSharedValue(0);
  const logoScale = useSharedValue(0.5);
  const taglineOpacity = useSharedValue(0);
  const taglineTranslateY = useSharedValue(30);

  useEffect(() => {
    // Animate logo entrance
    logoOpacity.value = withTiming(1, { duration: 800 });
    logoScale.value = withTiming(1, { duration: 800 });
    
    // Animate tagline entrance with delay
    taglineOpacity.value = withDelay(600, withTiming(1, { duration: 600 }));
    taglineTranslateY.value = withDelay(600, withTiming(0, { duration: 600 }));
    
    // Auto-hide splash screen after animation
    const timer = setTimeout(() => {
      onFinish();
    }, 2500);

    return () => clearTimeout(timer);
  }, []);

  const logoAnimatedStyle = useAnimatedStyle(() => ({
    opacity: logoOpacity.value,
    transform: [{ scale: logoScale.value }],
  }));

  const taglineAnimatedStyle = useAnimatedStyle(() => ({
    opacity: taglineOpacity.value,
    transform: [{ translateY: taglineTranslateY.value }],
  }));

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1BA3C7', '#0891B2']}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Logo Section */}
          <Animated.View style={[styles.logoContainer, logoAnimatedStyle]}>
            {/* Nurse Cap */}
            <View style={styles.nurseCap}>
              <View style={styles.capShape} />
              <View style={styles.crossVertical} />
              <View style={styles.crossHorizontal} />
            </View>
            
            {/* NV Letters */}
            <View style={styles.lettersContainer}>
              <Text style={styles.letterN}>N</Text>
              <Text style={styles.letterV}>V</Text>
            </View>
          </Animated.View>

          {/* Tagline Section */}
          <Animated.View style={[styles.taglineContainer, taglineAnimatedStyle]}>
            <Text style={styles.taglineText}>Expert Nursing Care at</Text>
            <Text style={styles.taglineText}>Your Doorstep</Text>
          </Animated.View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  nurseCap: {
    width: 80,
    height: 40,
    marginBottom: 20,
    position: 'relative',
  },
  capShape: {
    width: 80,
    height: 30,
    backgroundColor: 'white',
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  crossVertical: {
    position: 'absolute',
    top: 8,
    left: 35,
    width: 10,
    height: 20,
    backgroundColor: '#1BA3C7',
  },
  crossHorizontal: {
    position: 'absolute',
    top: 13,
    left: 25,
    width: 30,
    height: 10,
    backgroundColor: '#1BA3C7',
  },
  lettersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  letterN: {
    fontSize: 72,
    fontWeight: 'bold',
    color: 'white',
    marginRight: 5,
  },
  letterV: {
    fontSize: 72,
    fontWeight: 'bold',
    color: 'white',
  },
  taglineContainer: {
    alignItems: 'center',
  },
  taglineText: {
    fontSize: 18,
    color: 'white',
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: '400',
  },
});
