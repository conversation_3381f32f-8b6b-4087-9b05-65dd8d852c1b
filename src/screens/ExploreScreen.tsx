import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

const { width } = Dimensions.get('window');

export default function ExploreScreen() {
  const services = [
    {
      id: 1,
      title: 'Home Health Care',
      description: 'Comprehensive medical care in the comfort of your home',
      icon: 'home',
      color: '#10B981',
    },
    {
      id: 2,
      title: 'Wound Care',
      description: 'Professional wound assessment and treatment',
      icon: 'bandage',
      color: '#F59E0B',
    },
    {
      id: 3,
      title: 'Medication Management',
      description: 'Safe medication administration and monitoring',
      icon: 'medical',
      color: '#EF4444',
    },
    {
      id: 4,
      title: 'Post-Surgery Care',
      description: 'Specialized care after surgical procedures',
      icon: 'cut',
      color: '#8B5CF6',
    },
    {
      id: 5,
      title: 'Chronic Disease Management',
      description: 'Ongoing support for chronic conditions',
      icon: 'heart',
      color: '#EC4899',
    },
    {
      id: 6,
      title: 'Elder Care',
      description: 'Compassionate care for elderly patients',
      icon: 'people',
      color: '#06B6D4',
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Our Services</Text>
        <Text style={styles.headerSubtitle}>
          Professional nursing care tailored to your needs
        </Text>
      </View>

      <View style={styles.servicesGrid}>
        {services.map((service) => (
          <TouchableOpacity key={service.id} style={styles.serviceCard}>
            <View style={[styles.iconContainer, { backgroundColor: service.color }]}>
              <Icon name={service.icon} size={28} color="white" />
            </View>
            <Text style={styles.serviceTitle}>{service.title}</Text>
            <Text style={styles.serviceDescription}>{service.description}</Text>
            <TouchableOpacity style={styles.learnMoreButton}>
              <Text style={styles.learnMoreText}>Learn More</Text>
              <Icon name="arrow-forward" size={16} color="#1BA3C7" />
            </TouchableOpacity>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.ctaSection}>
        <Text style={styles.ctaTitle}>Need Immediate Care?</Text>
        <Text style={styles.ctaSubtitle}>
          Our nurses are available 24/7 for emergency situations
        </Text>
        <TouchableOpacity style={styles.emergencyButton}>
          <Icon name="call" size={20} color="white" />
          <Text style={styles.emergencyButtonText}>Call Emergency Line</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  servicesGrid: {
    padding: 20,
  },
  serviceCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 15,
  },
  learnMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  learnMoreText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1BA3C7',
    marginRight: 5,
  },
  ctaSection: {
    margin: 20,
    padding: 20,
    backgroundColor: '#1BA3C7',
    borderRadius: 12,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  ctaSubtitle: {
    fontSize: 14,
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.9,
  },
  emergencyButton: {
    flexDirection: 'row',
    backgroundColor: '#DC2626',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  emergencyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
