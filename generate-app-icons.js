const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// First, let's create a PNG version of the icon
const createBasePNG = async () => {
  const svgBuffer = Buffer.from(`
    <svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
      <rect width="1024" height="1024" fill="white"/>
      
      <!-- Nurse Cap -->
      <path d="M200 150 Q512 50 824 150 L750 250 Q512 200 274 250 Z" fill="#1BA3C7"/>
      
      <!-- Cross on Cap -->
      <rect x="487" y="100" width="50" height="100" fill="white"/>
      <rect x="412" y="125" width="200" height="50" fill="white"/>
      
      <!-- Letter N -->
      <path d="M100 300 L100 900 L180 900 L180 600 L330 900 L410 900 L410 300 L330 300 L330 600 L180 300 Z" fill="#1BA3C7"/>
      
      <!-- Letter V -->
      <path d="M614 300 L694 300 L794 700 L894 300 L974 300 L844 900 L744 900 Z" fill="#1BA3C7"/>
    </svg>
  `);
  
  await sharp(svgBuffer)
    .png()
    .toFile('./src/assets/images/app-icon-1024.png');
  
  console.log('✅ Created base 1024x1024 PNG icon');
};

async function generateAppIcons() {
  try {
    // Create base PNG first
    await createBasePNG();
    
    const iconPath = './src/assets/images/app-icon-1024.png';
    
    if (!fs.existsSync(iconPath)) {
      console.error('❌ Base icon file not found:', iconPath);
      return;
    }

    // Android icon sizes
    const androidSizes = {
      'mipmap-mdpi': 48,
      'mipmap-hdpi': 72,
      'mipmap-xhdpi': 96,
      'mipmap-xxhdpi': 144,
      'mipmap-xxxhdpi': 192,
    };

    console.log('🚀 Generating Android icons...');
    
    // Generate Android icons
    for (const [folder, size] of Object.entries(androidSizes)) {
      const outputDir = `./android/app/src/main/res/${folder}`;
      const outputPath = path.join(outputDir, 'ic_launcher.png');
      
      // Ensure directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      await sharp(iconPath)
        .resize(size, size, {
          kernel: sharp.kernel.lanczos3,
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .png()
        .toFile(outputPath);
      
      console.log(`✅ Generated Android ${folder}/ic_launcher.png (${size}x${size})`);
    }

    // Generate round icons for Android (adaptive icons)
    console.log('🚀 Generating Android round icons...');
    for (const [folder, size] of Object.entries(androidSizes)) {
      const outputDir = `./android/app/src/main/res/${folder}`;
      const outputPath = path.join(outputDir, 'ic_launcher_round.png');
      
      await sharp(iconPath)
        .resize(size, size, {
          kernel: sharp.kernel.lanczos3,
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .png()
        .toFile(outputPath);
      
      console.log(`✅ Generated Android ${folder}/ic_launcher_round.png (${size}x${size})`);
    }
    
    console.log('🎉 All Android app icons generated successfully!');
    console.log('📱 Android icons: android/app/src/main/res/mipmap-*/');
    
  } catch (error) {
    console.error('❌ Error generating icons:', error);
  }
}

generateAppIcons();
